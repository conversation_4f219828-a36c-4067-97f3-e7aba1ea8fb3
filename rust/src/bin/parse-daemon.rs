/*
cd ~/main/eterna/rust/ && \
cargo run --bin parse-daemon -- \
--source-log /FOO/BAR/BAZ/logs/2025-06-13--Fri.log \
--log-date 2020-01-02 \
--already-accomplished Sensor1 Sensor2 \
--sensor-list-of-names Sensor1 Sensor2 Sensor3 \
--sensor-list-of-names-and-addresses *********** Sensor1 *********** Sensor2 \
--sensor-dict-of-addresses-and-names '{"***********": "Sensor1", "***********": "Sensor2"}'
*/

use std::collections::{HashMap, HashSet};
use std::fs::File;
use std::io::{<PERSON>uf<PERSON><PERSON>, Buf<PERSON>eader};

use clap::Parser;
use rayon::prelude::*;
use serde_json;

use eterna::utils_classes::{
    DaemonConfig,
    DaemonParser,
};
use eterna::utils_parsers::{parse_ln, ConfigType};

#[derive(Parser, Debug)]
#[command(author, version, about)]
struct Args {
    #[arg(long = "source-log")]
    source_log: String,
    // /FOO/BAR/BAZ/logs/2025-06-13--Fri.log

    #[arg(long = "log-date")]
    log_date: String,
    // 2020-01-02

    #[arg(long = "already-accomplished", num_args = 0..)]
    already_accomplished: Vec<String>,
    // [] OR ["Sensor-1", "Sensor-2"]

    #[arg(long = "sensor-list-of-names", num_args = 1..)]
    sensor_list_of_names: Vec<String>,
    // ["Sensor-1", "Sensor-2", "Sensor-3"]

    #[arg(long = "sensor-list-of-names-and-addresses", num_args = 1..)]
    sensor_list_of_names_and_addresses: Vec<String>,
    // ["Sensor-1", "***********", "Sensor-2", "***********"]

    #[arg(long = "sensor-dict-of-addresses-and-names")]
    sensor_dict_of_addresses_and_names: String,
    // "{\"***********\": \"Sensor-1\", \"***********\": \"Sensor-2\"}"
}

// Parse line function equivalent to Python's parse_line
fn parse_line(ln: &str, already_accomplished: &HashSet<String>,
              sensor_list_of_names_and_addresses: &[String],
              sensor_dict_of_addresses_and_names: &HashMap<String, String>) -> (Option<String>, Option<Vec<String>>) {
    let (sensor_name, parsed_ln) = parse_ln(
        ln.trim(),
        ConfigType::Daemon,
        sensor_list_of_names_and_addresses,
        sensor_dict_of_addresses_and_names,
    );

    if let Some(ref name) = sensor_name {
        if already_accomplished.contains(name) {
            return (None, None);
        }
    }

    (sensor_name, parsed_ln)
}

fn main() {
    let args = Args::parse();

    // println!("Source log: {:?}", args.source_log);
    // println!("Log date: {:?}", args.log_date);
    // println!("Already accomplished: {:?}", args.already_accomplished);
    // println!("Sensor list of names: {:?}", args.sensor_list_of_names);
    // println!("Sensor list of names and addresses: {:?}", args.sensor_list_of_names_and_addresses);
    // println!("Sensor dict of addresses and names: {:?}", args.sensor_dict_of_addresses_and_names);

    // string -> dict
    let sensor_dict_of_addresses_and_names: HashMap<String, String> =
        serde_json::from_str(&args.sensor_dict_of_addresses_and_names)
            .expect("Failed to parse sensor_dict_of_addresses_and_names");

    // Convert already_accomplished to HashSet for O(1) lookups
    let already_accomplished: HashSet<String> = args.already_accomplished.into_iter().collect();

    // create dictionary of instances
    let log_date = &args.log_date;
    let mut sensor_names_and_instances: HashMap<String, DaemonParser> = args.sensor_list_of_names
        .iter()
        .map(|s_n| {
            (
                s_n.clone(),
                DaemonParser::new(
                    DaemonConfig::SLUG.value_string(),
                    log_date.to_string(),
                    s_n.to_string(),
                )
            )
        })
        .collect();

    println!("parsing...");

    // Stream file and process in chunks for better memory efficiency
    let file = File::open(&args.source_log).expect("Failed to open source log file");
    let reader = BufReader::new(file);

    // Process lines in parallel chunks to reduce memory usage and improve cache locality
    const CHUNK_SIZE: usize = 100_000; // Similar to Python's MYSQLConfig.POOL_CHUNKSIZE

    let lines: Vec<String> = reader.lines().collect::<Result<Vec<_>, _>>().expect("Failed to read lines");

    // Process all lines in parallel and collect results grouped by sensor name
    let results: Vec<(String, Vec<String>)> = lines
        .par_chunks(CHUNK_SIZE)
        .flat_map(|chunk| {
            chunk.par_iter().filter_map(|line| {
                let (sensor_name, parsed_ln) = parse_line(
                    line,
                    &already_accomplished,
                    &args.sensor_list_of_names_and_addresses,
                    &sensor_dict_of_addresses_and_names,
                );

                match (sensor_name, parsed_ln) {
                    (Some(name), Some(row)) => Some((name, row)),
                    _ => None,
                }
            })
        })
        .collect();

    // Group results by sensor name and batch insert to minimize allocations
    let mut sensor_results: HashMap<String, Vec<Vec<String>>> = HashMap::new();
    for (sensor_name, row) in results {
        sensor_results.entry(sensor_name).or_insert_with(Vec::new).push(row);
    }

    // Batch insert all rows for each sensor
    for (sensor_name, rows) in sensor_results {
        if let Some(parser) = sensor_names_and_instances.get_mut(&sensor_name) {
            parser.rows.extend(rows);
        }
    }

    // Print results showing how many lines have been parsed for each instance
    println!("\nParsing Results:");
    for (sensor_name, parser) in &sensor_names_and_instances {
        println!("{}: {} lines parsed", sensor_name, parser.rows.len());
    }

    println!("\nParser Finished Successfully");
}
