/*
cd ~/main/eterna/rust/ && \
cargo run --bin parse-daemon -- \
--source-log /FOO/BAR/BAZ/logs/2025-06-13--Fri.log \
--log-date 2020-01-02 \
--already-accomplished Sensor1 Sensor2 \
--sensor-list-of-names Sensor1 Sensor2 Sensor3 \
--sensor-list-of-names-and-addresses *********** Sensor1 *********** Sensor2 \
--sensor-dict-of-addresses-and-names '{"***********": "Sensor1", "***********": "Sensor2"}'
*/

use std::collections::HashMap;
use std::fs::File;
use std::io::{<PERSON><PERSON><PERSON><PERSON>, <PERSON>ufReader};
use std::sync::{Arc, Mutex};

use clap::Parser;
use rayon::prelude::*;
use serde_json;

use eterna::utils_classes::{
    DaemonConfig,
    DaemonParser,
};
use eterna::utils_parsers::{parse_ln, ConfigType};

#[derive(Parser, Debug)]
#[command(author, version, about)]
struct Args {
    #[arg(long = "source-log")]
    source_log: String,
    // /FOO/BAR/BAZ/logs/2025-06-13--Fri.log

    #[arg(long = "log-date")]
    log_date: String,
    // 2020-01-02

    #[arg(long = "already-accomplished", num_args = 0..)]
    already_accomplished: Vec<String>,
    // [] OR ["Sensor-1", "Sensor-2"]

    #[arg(long = "sensor-list-of-names", num_args = 1..)]
    sensor_list_of_names: Vec<String>,
    // ["Sensor-1", "Sensor-2", "Sensor-3"]

    #[arg(long = "sensor-list-of-names-and-addresses", num_args = 1..)]
    sensor_list_of_names_and_addresses: Vec<String>,
    // ["Sensor-1", "***********", "Sensor-2", "***********"]

    #[arg(long = "sensor-dict-of-addresses-and-names")]
    sensor_dict_of_addresses_and_names: String,
    // "{\"***********\": \"Sensor-1\", \"***********\": \"Sensor-2\"}"
}

// Parse line function equivalent to Python's parse_line
fn parse_line(ln: &str, already_accomplished: &[String],
              sensor_list_of_names_and_addresses: &[String],
              sensor_dict_of_addresses_and_names: &HashMap<String, String>) -> (Option<String>, Option<Vec<String>>) {
    let (sensor_name, parsed_ln) = parse_ln(
        ln.trim(),
        ConfigType::Daemon,
        sensor_list_of_names_and_addresses,
        sensor_dict_of_addresses_and_names,
    );

    if let Some(ref name) = sensor_name {
        if already_accomplished.contains(name) {
            return (None, None);
        }
    }

    (sensor_name, parsed_ln)
}

fn main() {
    let args = Args::parse();

    // string -> dict
    let sensor_dict_of_addresses_and_names: HashMap<String, String> =
        serde_json::from_str(&args.sensor_dict_of_addresses_and_names)
            .expect("Failed to parse sensor_dict_of_addresses_and_names");

    // create dictionary of instances
    let log_date = &args.log_date;
    let sensor_names_and_instances: Arc<Mutex<HashMap<String, DaemonParser>>> = Arc::new(Mutex::new(
        args.sensor_list_of_names
            .iter()
            .map(|s_n| {
                (
                    s_n.clone(),
                    DaemonParser::new(
                        DaemonConfig::SLUG.value_string(),
                        log_date.to_string(),
                        s_n.to_string(),
                    )
                )
            })
            .collect()
    ));

    // Read file and process lines in parallel
    let file = File::open(&args.source_log).expect("Failed to open source log file");
    let reader = BufReader::new(file);
    let lines: Vec<String> = reader.lines().collect::<Result<Vec<_>, _>>().expect("Failed to read lines");

    println!("parsing...");

    // Process lines in parallel using rayon
    let results: Vec<(Option<String>, Option<Vec<String>>)> = lines
        .par_iter()
        .map(|line| {
            parse_line(
                line,
                &args.already_accomplished,
                &args.sensor_list_of_names_and_addresses,
                &sensor_dict_of_addresses_and_names,
            )
        })
        .collect();

    // Collect results into sensor instances
    for (sensor_name, parsed_ln) in results {
        if let (Some(name), Some(row)) = (sensor_name, parsed_ln) {
            let mut instances = sensor_names_and_instances.lock().unwrap();
            if let Some(parser) = instances.get_mut(&name) {
                parser.rows.push(row);
            }
        }
    }

    // Print results showing how many lines have been parsed for each instance
    let instances = sensor_names_and_instances.lock().unwrap();
    println!("\nParsing Results:");
    for (sensor_name, parser) in instances.iter() {
        println!("{}: {} lines parsed", sensor_name, parser.rows.len());
    }

    println!("\nParser Finished Successfully");
}
