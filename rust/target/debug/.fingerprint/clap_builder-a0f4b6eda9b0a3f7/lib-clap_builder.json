{"rustc": 2697959624369812960, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 15221872889701672926, "path": 11903153657433936045, "deps": [[5820056977320921005, "anstream", false, 9623230471314357468], [9394696648929125047, "anstyle", false, 5349224141870135130], [11166530783118767604, "strsim", false, 3125369490231316242], [11649982696571033535, "clap_lex", false, 15198100787298857552]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap_builder-a0f4b6eda9b0a3f7/dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}